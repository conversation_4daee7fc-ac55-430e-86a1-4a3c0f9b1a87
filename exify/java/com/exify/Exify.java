package com.exify;

public class Exify {
    static {
        System.loadLibrary("exify"); // Make sure the library name matches your built artifact
    }

    // Native method to load grammar
    public static native Object loadGrammar(String grammarFilePath);

    // Native method to encode JSON data
    public static native byte[] encode(String jsonData);

    // Native method to decode encoded bytes
    public static native String decode(byte[] encodedBytes);
}
