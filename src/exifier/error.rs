#[derive(thiserror::<PERSON><PERSON><PERSON>, Debug)]
pub enum Error {
    #[error("Generic {0}")]
    Generic(String),
    #[error(transparent)]
    IO(#[from] std::io::Error),
}

impl PartialEq for Error {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (Error::Generic(a), Error::Generic(b)) => a == b,
            // For IO errors, we compare the error kind and message
            (Error::IO(a), Error::IO(b)) => a.kind() == b.kind() && a.to_string() == b.to_string(),
            _ => false,
        }
    }
}
