use serde_json::Value;

/// Converts a JSON Schema to an XML Schema (XSD) according to EXI4JSON mapping rules.
/// This is a best-effort mapping for common types. Not all JSON Schema features are supported.
pub fn convert_to_xml_schema(json_schema: &str) -> Result<String, String> {
    let schema: Value = serde_json::from_str(json_schema)
        .map_err(|e| format!("Failed to parse JSON Schema: {}", e))?;
    let mut xsd = String::new();
    // No XML header or xs:schema wrapper, as per your required output
    xsd.push_str(&json_schema_to_custom_xsd_element(
        &schema,
        "map",
        schema.get("required"),
    ));
    Ok(xsd)
}

fn json_schema_to_custom_xsd_element(
    schema: &Value,
    name: &str,
    required: Option<&Value>,
) -> String {
    let mut xsd = String::new();
    match schema.get("type").and_then(|v| v.as_str()) {
        Some("object") => {
            xsd.push_str(&format!(
                "<element name=\"{}\">\n    <complexType>\n        <all>\n",
                name
            ));
            if let Some(props) = schema.get("properties").and_then(|v| v.as_object()) {
                let required_props = required
                    .and_then(|v| v.as_array())
                    .map(|arr| arr.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>());
                for (prop, prop_schema) in props {
                    let min_occurs = if let Some(ref req) = required_props {
                        if req.contains(&prop.as_str()) {
                            ""
                        } else {
                            " minOccurs=\"0\""
                        }
                    } else {
                        " minOccurs=\"0\""
                    };
                    xsd.push_str(&format!(
                        "            <element{} name=\"{}\">\n{}            </element>\n",
                        min_occurs,
                        prop,
                        json_schema_property_to_xsd(prop_schema)
                    ));
                }
            }
            xsd.push_str("        </all>\n    </complexType>\n</element>\n");
        }
        Some("array") => {
            if let Some(items) = schema.get("items") {
                // For a homogeneous array, output the required structure
                let mut xsd_array = String::new();
                xsd_array.push_str("<element name=\"array\">\n    <complexType>\n        <sequence maxOccurs=\"unbounded\" minOccurs=\"0\">\n");
                match items.get("type").and_then(|v| v.as_str()) {
                    Some("number") | Some("integer") => {
                        xsd_array
                            .push_str("            <element name=\"number\" type=\"double\"/>\n");
                    }
                    Some("string") => {
                        xsd_array
                            .push_str("            <element name=\"string\" type=\"string\"/>\n");
                    }
                    Some("boolean") => {
                        xsd_array
                            .push_str("            <element name=\"boolean\" type=\"boolean\"/>\n");
                    }
                    _ => {
                        xsd_array.push_str("            <!-- Unsupported or missing type -->\n");
                    }
                }
                xsd_array.push_str("        </sequence>\n    </complexType>\n</element>\n");
                return xsd_array;
            }
        }
        Some("string") => {
            if let Some(enum_values) = schema.get("enum").and_then(|v| v.as_array()) {
                let mut values: Vec<String> = enum_values
                    .iter()
                    .filter_map(|v| v.as_str().map(|s| s.to_string()))
                    .collect();
                // Do not sort, preserve input order
                let mut xsd_enum = String::new();
                xsd_enum.push_str("<element name=\"string\">\n    <simpleType>\n        <restriction base=\"string\">\n");
                for v in values {
                    xsd_enum.push_str(&format!("            <enumeration value=\"{}\"/>\n", v));
                }
                xsd_enum.push_str("        </restriction>\n    </simpleType>\n</element>");
                return xsd_enum;
            }
            return "<element name=\"string\" type=\"string\"/>".to_string();
        }
        Some("number") | Some("integer") => {
            return "<element name=\"number\" type=\"double\"/>".to_string();
        }
        Some("boolean") => {
            return "<element name=\"boolean\" type=\"boolean\"/>".to_string();
        }
        Some("null") => {
            return "<element name=\"null\">\n    <complexType/>\n</element>".to_string();
        }
        _ => {
            xsd.push_str(&format!(
                "<!-- Unsupported or missing type for property '{}' -->\n",
                name
            ));
        }
    }
    xsd
}

fn json_schema_property_to_xsd(schema: &Value) -> String {
    match schema.get("type").and_then(|v| v.as_str()) {
        Some("string") => {
            "                <complexType>\n                    <sequence>\n                        <!--unnamed-format-->\n                        <element name=\"string\" type=\"string\"/>\n                    </sequence>\n                </complexType>\n".to_string()
        }
        Some("number") | Some("integer") => {
            "                <complexType>\n                    <sequence>\n                        <element name=\"number\" type=\"double\"/>\n                    </sequence>\n                </complexType>\n".to_string()
        }
        Some("boolean") => {
            "                <complexType>\n                    <sequence>\n                        <element name=\"boolean\" type=\"boolean\"/>\n                    </sequence>\n                </complexType>\n".to_string()
        }
        _ => {
            "                <!-- Unsupported or missing type -->\n".to_string()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_convert_simple_object_schema() {
        let json_schema = r#"{
  "type": "object",
  "properties": {
    "firstname": { "type": "string" },
    "lastname": { "type": "string" },
    "age": { "type": "number" }
  },
  "required": ["lastname", "age"]
}"#;
        let xsd = convert_to_xml_schema(json_schema).expect("Conversion should succeed");
        let expected = r#"<element name="map">
    <complexType>
        <all>
            <element minOccurs="0" name="firstname">
                <complexType>
                    <sequence>
                        <!--unnamed-format-->
                        <element name="string" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="lastname">
                <complexType>
                    <sequence>
                        <!--unnamed-format-->
                        <element name="string" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="age">
                <complexType>
                    <sequence>
                        <element name="number" type="double"/>
                    </sequence>
                </complexType>
            </element>
        </all>
    </complexType>
</element>
"#;
        assert_eq!(xsd.trim(), expected.trim());
    }

    #[test]
    fn test_convert_array_of_numbers() {
        let json_schema = r#"{
  "type": "array",
  "items": {
    "type": "number"
  }
}"#;
        let xsd = convert_to_xml_schema(json_schema).expect("Conversion should succeed");
        let expected = r#"<element name="array">
    <complexType>
        <sequence maxOccurs="unbounded" minOccurs="0">
            <element name="number" type="double"/>
        </sequence>
    </complexType>
</element>
"#;
        assert_eq!(xsd.trim(), expected.trim());
    }

    #[test]
    fn test_convert_string_type() {
        let json_schema = r#"{
  "type": "string"
}"#;
        let xsd = convert_to_xml_schema(json_schema).expect("Conversion should succeed");
        let expected = r#"<element name="string" type="string"/>"#;
        assert_eq!(xsd.trim(), expected.trim());
    }

    #[test]
    fn test_convert_number_type() {
        let json_schema = r#"{
  "type": "number"
}"#;
        let xsd = convert_to_xml_schema(json_schema).expect("Conversion should succeed");
        let expected = r#"<element name="number" type="double"/>"#;
        assert_eq!(xsd.trim(), expected.trim());
    }

    #[test]
    fn test_convert_boolean_type() {
        let json_schema = r#"{
  "type": "boolean"
}"#;
        let xsd = convert_to_xml_schema(json_schema).expect("Conversion should succeed");
        let expected = r#"<element name="boolean" type="boolean"/>"#;
        assert_eq!(xsd.trim(), expected.trim());
    }

    #[test]
    fn test_convert_null_type() {
        let json_schema = r#"{
  "type": "null"
}"#;
        let xsd = convert_to_xml_schema(json_schema).expect("Conversion should succeed");
        let expected = r#"<element name="null">
    <complexType/>
</element>"#;
        assert_eq!(xsd.trim(), expected.trim());
    }

    #[test]
    fn test_convert_enum_string_type() {
        let json_schema = r#"{
  "type": "string",
  "enum": ["red", "amber", "green"]
}"#;
        let xsd = convert_to_xml_schema(json_schema).expect("Conversion should succeed");
        let expected = r#"<element name="string">
    <simpleType>
        <restriction base="string">
            <enumeration value="red"/>
            <enumeration value="amber"/>
            <enumeration value="green"/>
        </restriction>
    </simpleType>
</element>"#;
        assert_eq!(xsd.trim(), expected.trim());
    }
}
