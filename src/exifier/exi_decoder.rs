use crate::exifier::grammar::{EXIGrammar, Production};
use crate::exifier::prelude::*;
use crate::exifier::reader::BitStreamReader;
use serde_json::{Map, Number, Value};

#[derive(Debug)]
pub struct ExiDecoder {
    grammar: EXIGrammar,
    json_object: Map<String, Value>,
    bitstream_reader: BitStreamReader,
}

impl ExiDecoder {
    pub fn new() -> Self {
        ExiDecoder {
            grammar: EXIGrammar::default(),
            json_object: Map::new(),
            bitstream_reader: BitStreamReader::default(),
        }
    }

    pub fn load_grammar(&mut self, exi_grammar: EXIGrammar) -> Result<()> {
        tracing::debug!(
            "Loading EXI grammar with {} grammar rules",
            exi_grammar.grs.grammar.len()
        );
        tracing::debug!(
            "Document grammar ID: {}",
            exi_grammar.grs.document_grammar_id
        );
        tracing::debug!(
            "Fragment grammar ID: {}",
            exi_grammar.grs.fragment_grammar_id
        );
        // TODO: Store the grammar for use in encoding/decoding
        self.grammar = exi_grammar;
        Ok(())
    }

    pub fn decode(&mut self, encoded_bytes: Vec<u8>) -> Result<Value> {
        // Decode the incoming bytes to a string
        self.bitstream_reader = BitStreamReader::new(encoded_bytes.clone());

        self.decode_header()?; // This is where document grammar begins.
        self.decode_body()?;

        tracing::debug!("Decoded bytes to JSON string: {:#?}", self.json_object);

        if self.json_object.is_empty() {
            return Err(crate::exifier::Error::Generic(format!(
                "Failed to decode JSON"
            )));
        }
        Ok(Value::Object(self.json_object.clone()))
    }

    fn decode_header(&mut self) -> Result<()> {
        let first_two_bits = self.bitstream_reader.read_n_bit_data(2)?;
        if first_two_bits != 2 {
            return Err(crate::exifier::Error::Generic(format!(
                "Invalid header. Expected 2, got {}",
                first_two_bits
            )));
        }

        let options_bit = self.bitstream_reader.read_n_bit_data(1)?;
        if options_bit != 0 {
            return Err(crate::exifier::Error::Generic(format!(
                "Invalid header. Expected 0, got {}",
                options_bit
            )));
        }

        let header_version = self.bitstream_reader.read_n_bit_data(5)?;
        if header_version != 0 {
            return Err(crate::exifier::Error::Generic(format!(
                "Invalid header. Expected 0, got {}",
                header_version
            )));
        }

        tracing::debug!(
            "Decoded header. Options: {}, Header version: {}",
            options_bit,
            header_version
        );
        Ok(())
    }

    fn decode_body(&mut self) -> Result<()> {
        let mut opened_elements: Vec<String> = Vec::new();
        let mut current_grammar_id: i32 = 1;
        let mut map = Map::new();
        let mut current_value = Value::Null;
        let mut open_grammar_stack: Vec<i32> = Vec::new();
        loop {
            tracing::debug!("Next grammar id: {:?}", current_grammar_id);
            let current_prod = self
                .grammar
                .grs
                .grammar
                .iter()
                .find(|g| g.grammar_id == current_grammar_id.to_string())
                .unwrap();
            let bits_to_read = (current_prod.production.len() as f32).log2().ceil() as u8;
            let prod_index = self.bitstream_reader.read_n_bit_data(bits_to_read)?;
       //     open_grammar_stack.push(current_grammar_id);

            tracing::debug!("Bits to read: {} {}", bits_to_read, prod_index);
            if prod_index >= current_prod.production.len() as u8 {
                return Err(crate::exifier::Error::Generic(format!(
                    "Invalid production index. Expected 0-{}, got {}",
                    current_prod.production.len() - 1,
                    prod_index
                )));
            }
            let prod = &current_prod.production[prod_index as usize];
            match prod.event.as_str() {
                "startElement" => {
                    tracing::trace!("Start element: {:?}", prod);
                    // opened_elements.push(pr);

                    if let Some(namespace) = self
                        .grammar
                        .qnames
                        .namespace_context
                        .get(prod.start_element_namespace_id.unwrap() as usize)
                    {
                        if let Some(local_name) = namespace
                            .qname_context
                            .iter()
                            .find(|q| q.local_name_id == prod.start_element_local_name_id.unwrap())
                        {
                            if bits_to_read == 0 {
                                tracing::debug!("This start element was interpretted with 0 bits. Should this be pushed to the stack?");
                                //    }
                                //   else{
                            } 
                         //   grammar_event_stack.push(current_grammar_id);
                         else {
                            tracing::debug!("Pushing grammar id to stack: {}", current_grammar_id);
                            open_grammar_stack.push(current_grammar_id);
                         }
                            opened_elements.push(local_name.local_name.clone());
                            tracing::debug!("Pushed element: {}", local_name.local_name.clone());
                        }
                    }

                    current_grammar_id = prod.start_element_grammar_id.unwrap() as i32;
                  //  open_grammar_stack.push(current_grammar_id);

                    tracing::debug!(
                        "Prod event stack: {:#?}\n Open elements: {:#?}",
                        open_grammar_stack,
                        opened_elements
                    );
                }
                "endElement" => {
                    tracing::debug!("End element: {:?}", prod);
                    tracing::debug!("Open elements: {:?}", opened_elements);
                  //  tracing::debug!("Grammar stack: {:?}", grammar_event_stack);
                    if bits_to_read == 0 {
                        tracing::debug!("This end element was interpretted with 0 bits. Should this be popped from the stack?");
                        tracing::debug!("Open elements: {:?} (Before pop)", opened_elements);
                        opened_elements.pop();
                        if opened_elements.is_empty() {
                            tracing::debug!("Stack empty");
                            break;
                        }
                        continue;
                    }
                    // if let Some(element) = opened_elements.pop() {
                    //    // map = map.insert(element, Value::Object(map.clone()));
                    // }
                    // match grammar_event_stack.pop() {
                    //     Some(grammar_id) => {
                    //         current_grammar_id = grammar_id;
                    //     }
                    //     None => {
                    //         tracing::debug!("Stack empty");
                    //         break;
                    //     }
                    // }

                    // tracing::debug!("Grammar event stack: {:#?}", grammar_event_stack);
                    // if grammar_event_stack.is_empty() {
                    //     break;
                    // }
                    // if current_grammar_id == 1 {
                    //    break;
                    // }
                    // if let Some(grammar_id) = next_grammar_id {
                    //     //      current_grammar_id = grammar_id;
                    // }
                    // current_grammar_id = prod.next_grammar_id.unwrap() as i32;
                    //if let Some(prod) = prod_event_stack.pop() {
                        current_grammar_id = open_grammar_stack.pop().unwrap();
                    //}

                    tracing::debug!(
                        "Prod event stack: {:#?}\n Open elements: {:#?}",
                        open_grammar_stack,
                        opened_elements
                    );
                }
                "characters" => {
                    let mut int_data: i64 = 0;
                   // current_grammar_id = prod.next_grammar_id.unwrap() as i32;
                    match opened_elements.pop() {
                        Some(number) if number == "number" => {
                            let sign = self.bitstream_reader.read_n_bit_data(1)?;
                            tracing::debug!("Read sign: {}", sign);
                            let mut data: i128 = self.read_unsigned_int()? as i128;
                            if sign == 1 {
                                // Negative numbers are stored as one less that their actual value.
                                // So, on decode, add one to the magnitude and flip the sign.
                                data += 1;
                                data *= -1;
                            }

                            // Convert i128 to i64, clamping to i64 range if necessary
                            int_data = if data > i64::MAX as i128 {
                                i64::MAX
                            } else if data < i64::MIN as i128 {
                                i64::MIN
                            } else {
                                data as i64
                            };

                            tracing::debug!("Read integer: {}", data);
                            current_value = Value::Number(Number::from(int_data));
                            map.insert(
                                opened_elements.last().unwrap().clone(),
                                current_value.clone(),
                            );
                            tracing::debug!("Current map: {:#?}", map);
                        }
                        Some(string) if string == "string" => {
                            let mut length = self.read_unsigned_int()?;
                            let mut encoded_string = Vec::new();
                            if length > 2 {
                                length -= 2;

                                for _i in 0..length {
                                    encoded_string.push(self.bitstream_reader.read_n_bit_data(8)?);
                                }
                                let decoded_string = str::from_utf8(&encoded_string).unwrap();
                                current_value = Value::String(String::from(decoded_string));
                                tracing::debug!("Read string: {}", decoded_string);
                                map.insert(
                                    opened_elements.last().unwrap().clone(),
                                    current_value.clone(),
                                );
                            }
                            //break;
                        }
                        Some(unknown_element_type) => {
                            tracing::debug!("Unsupported type {:?}", unknown_element_type);
                        }
                        None => {
                            tracing::debug!("Stack empty");
                            break;
                        }
                    }

                    let element_type = opened_elements.pop();
                    match element_type {
                        Some(element_type) if element_type == "number" => {
                            current_value = Value::Number(Number::from(int_data));
                            tracing::debug!("Read number: {}", current_value);
                        }
                        None => {
                            // tracing::debug!("Stack empty");
                            //break;
                        }
                        _ => {}
                    }
                    //tracing::debug!("Also pop the last element from grammar stack: {:#?}\n Grammar stack: {:#?}", grammar_event_stack.pop(), grammar_event_stack);
                    //current_grammar_id = grammar_event_stack.pop().unwrap();
                    current_grammar_id = prod.next_grammar_id.unwrap() as i32;
                  //  if let Some(prod) = prod_event_stack.pop() {
                        //current_grammar_id = prod.next_grammar_id.unwrap() as i32;
                    //} else {
                      //  tracing::debug!("Stack empty");
                       // break;
                   // }
                }
                "endDocument" => {
                    tracing::debug!("End document: {:?}", prod);
                    break;
                }
                _ => {
                    tracing::warn!("Unknown event: {:?}", prod);
                }
            }
        }
        //self.json_object.insert("map".to_string(), Value::Object(map));
        tracing::debug!("Final map: {:#?}", map);
        self.json_object = map;
        Ok(())
    }

    fn read_unsigned_int(&mut self) -> Result<u128> {
        let mut result: u128 = self.bitstream_reader.read_n_bit_data(8)? as u128;

        // < 128: just one byte, optimal case
        // ELSE: multiple bytes...
        if result >= 128 {
            result &= 127;
            let mut m_shift = 7;
            let mut b;

            loop {
                // 1. Read the next octet
                b = self.bitstream_reader.read_n_bit_data(8)?;
                // 2. Multiply the value of the unsigned number represented by
                // the 7 least significant
                // bits of the octet by the current multiplier and add the
                // result to the current value.
                result += ((b & 127) << m_shift) as u128;
                // 3. Multiply the multiplier by 128
                m_shift += 7;
                // 4. If the most significant bit of the octet was 1, go back to
                // step 1

                if b < 128 || m_shift > 128 {
                    break;
                }
            }
        }

        Ok(result)
    }
}
