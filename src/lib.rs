mod exifier;
use exifier::{EXIGrammar, Exifier};

use jni::objects::{JByteArray, JObject, JString};
use jni::sys::{jbyteArray, jlong, jobject};
use jni::JNIEnv;

use std::ptr;

// Dummy struct to represent native state
#[derive(Default)]
struct JavaExifier {
    inner: Exifier,
}

// Helper to convert pointer to jlong
fn to_jlong(ptr: *mut JavaExifier) -> jlong {
    ptr as jlong
}

// Helper to convert jlong to pointer
fn from_jlong(handle: jlong) -> *mut JavaExifier {
    handle as *mut JavaExifier
}

#[no_mangle]
pub extern "system" fn Java_com_exify_ExifierProcessor_createExifier(
    _env: JNIEnv,
    _class: JObject,
) -> jlong {
    let exifier = Box::new(JavaExifier {
        inner: Exifier::default(),
    });
    to_jlong(Box::into_raw(exifier))
}

#[no_mangle]
pub extern "system" fn Java_com_exify_ExifierProcessor_destroyExifier(
    _env: JNIEnv,
    _class: JObject,
    handle: jlong,
) {
    if handle != 0 {
        drop(unsafe { Box::from_raw(from_jlong(handle)) });
    }
}

#[no_mangle]
pub extern "system" fn Java_com_exify_ExifierProcessor_loadGrammar(
    mut env: JNIEnv,
    _class: JObject,
    handle: jlong,
    grammar_file_path: JString,
) {
    let exifier = unsafe { from_jlong(handle).as_mut() };
    if let Some(exifier) = exifier {
        if let Ok(path) = env.get_string(&grammar_file_path) {
            let path_str: String = path.into();
            match EXIGrammar::from_file(&path_str) {
                Ok(exi_grammar) => {
                    let _ = exifier.inner.load_grammar(exi_grammar);
                }
                Err(_e) => {
                    // Optionally: log or handle the error
                }
            }
        }
    }
}

#[no_mangle]
pub extern "system" fn Java_com_exify_ExifierProcessor_encode(
    mut env: JNIEnv,
    _class: JObject,
    handle: jlong,
    json_data: JString,
) -> jbyteArray {
    let exifier = unsafe { from_jlong(handle).as_mut() };
    if let Some(exifier) = exifier {
        let json_string: String = match env.get_string(&json_data) {
            Ok(s) => s.into(),
            Err(_) => return ptr::null_mut(),
        };
        let json_value: serde_json::Value = match serde_json::from_str(&json_string) {
            Ok(val) => val,
            Err(_) => return ptr::null_mut(),
        };
        match exifier.inner.encode(json_value) {
            Ok(encoded) => match env.byte_array_from_slice(&encoded) {
                Ok(byte_array) => byte_array.into_raw(),
                Err(_) => ptr::null_mut(),
            },
            Err(_) => ptr::null_mut(),
        }
    } else {
        ptr::null_mut()
    }
}

#[no_mangle]
pub extern "system" fn Java_com_exify_ExifierProcessor_decode(
    env: JNIEnv,
    _class: JObject,
    handle: jlong,
    encoded_bytes: jbyteArray,
) -> jobject {
    let exifier = unsafe { from_jlong(handle).as_mut() };
    if let Some(exifier) = exifier {
        let byte_array = unsafe { JByteArray::from_raw(encoded_bytes) };
        let bytes_vec = match env.convert_byte_array(byte_array) {
            Ok(bytes) => bytes,
            Err(_) => return ptr::null_mut(),
        };
        match exifier.inner.decode(bytes_vec) {
            Ok(json_value) => {
                let json_string = match serde_json::to_string(&json_value) {
                    Ok(s) => s,
                    Err(_) => return ptr::null_mut(),
                };
                match env.new_string(json_string) {
                    Ok(jstr) => jstr.into_raw(),
                    Err(_) => ptr::null_mut(),
                }
            }
            Err(_) => ptr::null_mut(),
        }
    } else {
        ptr::null_mut()
    }
}
