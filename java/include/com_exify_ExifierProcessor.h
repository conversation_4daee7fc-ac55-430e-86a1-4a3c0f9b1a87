/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_exify_ExifierProcessor */

#ifndef _Included_com_exify_ExifierProcessor
#define _Included_com_exify_ExifierProcessor
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_exify_ExifierProcessor
 * Method:    createExifier
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_exify_ExifierProcessor_createExifier
  (JNIEnv *, jclass);

/*
 * Class:     com_exify_ExifierProcessor
 * Method:    destroyExifier
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_com_exify_ExifierProcessor_destroyExifier
  (JNIEnv *, jclass, jlong);

/*
 * Class:     com_exify_ExifierProcessor
 * Method:    loadGrammar
 * Signature: (JLjava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_exify_ExifierProcessor_loadGrammar
  (JNIEnv *, jclass, jlong, jstring);

/*
 * Class:     com_exify_ExifierProcessor
 * Method:    encode
 * Signature: (JLjava/lang/String;)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_exify_ExifierProcessor_encode
  (JNIEnv *, jclass, jlong, jstring);

/*
 * Class:     com_exify_ExifierProcessor
 * Method:    decode
 * Signature: (J[B)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_exify_ExifierProcessor_decode
  (JNIEnv *, jclass, jlong, jbyteArray);

#ifdef __cplusplus
}
#endif
#endif
