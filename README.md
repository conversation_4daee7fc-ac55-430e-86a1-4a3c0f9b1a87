# exify

## Building the Rust JNI Library and Java Bindings

This project provides Rust and Java bindings for Exify using JNI.

### Prerequisites
- Rust toolchain (https://rustup.rs)
- Java JDK (javac, jar)
- Make

### Build Steps

1. **Build everything (Rust + Java):**
   ```sh
   make all
   ```
   This will build the Rust shared library and compile the Java sources.

2. **Build only the Rust JNI library:**
   ```sh
   make rust
   ```
   The shared library will be placed in `java/build/`.

3. **Compile Java sources:**
   ```sh
   make java
   ```

4. **Generate JNI headers:**
   ```sh
   make jni
   ```
   JNI headers will be placed in `java/include/`.

5. **Package Java classes into a JAR:**
   ```sh
   make jar
   ```
   The JAR will be created at `java/build/exifier_java.jar`.

6. **Clean build artifacts:**
   ```sh
   make clean
   ```

### Notes
- The Rust shared library is named `libexifier_java.dylib` (macOS) or `libexifier_java.so` (Linux) and is copied to `java/build/`.
- Java sources are expected in `java/com/exify/`.
- Update the `System.loadLibrary` call in your Java code if you change the library name.

### Example Usage
See `java/com/exify/ExifierProcessor.java` for an example of how to use the Java bindings.