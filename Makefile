# Paths
RUST_CRATE=.
JAVA_SRC_DIR=java/com/exify
JAVA_OUT_DIR=java/build
JNI_HEADERS_DIR=java/include
LIB_NAME=exify

# Detect OS for shared library extension
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Darwin)
    LIB_EXT=dylib
else
    LIB_EXT=so
endif

.PHONY: all clean rust java jni run-example

all: rust java

rust:
	mkdir -p $(JAVA_OUT_DIR)
	cargo build --release --manifest-path $(RUST_CRATE)/Cargo.toml
	cp target/release/lib$(LIB_NAME).$(LIB_EXT) $(JAVA_OUT_DIR)/

java:
	mkdir -p $(JAVA_OUT_DIR)
	javac -d $(JAVA_OUT_DIR) $(JAVA_SRC_DIR)/*.java

jni:
	mkdir -p $(JNI_HEADERS_DIR)
	javac -h $(JNI_HEADERS_DIR) -d $(JAVA_OUT_DIR) $(JAVA_SRC_DIR)/*.java

jar:
	jar cf $(JAVA_OUT_DIR)/exifier_java.jar -C $(JAVA_OUT_DIR) .

run-example:
	javac -d java/build -cp java/build example/ExampleMain.java
	java -cp java/build -Djava.library.path=java/build example.ExampleMain

clean:
	rm -rf $(JAVA_OUT_DIR) $(JNI_HEADERS_DIR)
	cargo clean --manifest-path $(RUST_CRATE)/Cargo.toml
