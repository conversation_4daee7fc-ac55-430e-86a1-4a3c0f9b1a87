[package]
name = "exify"
version = "0.1.0"
edition = "2021"

[lib]
name = "exify"
crate-type = ["cdylib"]

[dependencies]
thiserror = "^1.0.40"
tokio = { version = "1.44.1", features = ["full"] }
quick-xml = "0.37.5"
tracing = "0.1"
tracing-subscriber = "0.3"
xml2json-rs = "1.0.1"
clap = { version = "^4.5.20", features = ["derive", "env", "string"] }
serde_json = "1.0.140"
serde = { version = "1.0.219", features = ["derive"] }
jni = "0.21"



[dev-dependencies]
anyhow = "1.0.98"



