package example;

import com.exify.ExifierProcessor;
import java.nio.file.Files;
import java.nio.file.Paths;

public class ExampleMain {
    public static void main(String[] args) {
        try (ExifierProcessor processor = new ExifierProcessor()) {
            // Load grammar from test_schema.xsd.grs
            processor.loadGrammar("resources/examples/example_1/test_schema.xsd.grs");

            // Read JSON from test_schema.json
            String testJson = new String(Files.readAllBytes(Paths.get("resources/examples/example_1/test_json_sample.json")));
            System.out.println("Original JSON: " + testJson);
            System.out.println("Original JSON length: " + testJson.length());
            // Encode JSON to EXI bytes
            byte[] encoded = processor.encode(testJson);
            System.out.println("Encoded bytes length: " + encoded.length);

            // Decode EXI bytes back to JSON
            String decoded = processor.decode(encoded);
            System.out.println("Decoded JSON: " + decoded);
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
